<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Leads_reassign extends AdminController {

	public function __construct()
	{
		parent::__construct();
	}

	public function reassign_lead()
	{
		if (!$this->input->is_ajax_request()) {
			show_404();
		}

		$lead_id = $this->input->post('lead_id');
		$new_assigned_staff_id = $this->input->post('new_assigned_staff_id');

		if (empty($lead_id) || empty($new_assigned_staff_id)) {
			echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
			return;
		}

		// Check permission and ownership
		if (!apm_can_reassign_lead($lead_id)) {
			echo json_encode(['success' => false, 'message' => _l('apm_reassign_permission_denied')]);
			return;
		}

		// Perform the reassignment
		$result = apm_reassign_lead($lead_id, $new_assigned_staff_id);

		if ($result) {
			echo json_encode(['success' => true, 'message' => _l('apm_reassign_lead_success')]);
		} else {
			echo json_encode(['success' => false, 'message' => _l('apm_reassign_lead_error')]);
		}
	}

	public function get_assignable_staff()
	{
		if (!$this->input->is_ajax_request()) {
			show_404();
		}

		$lead_id = $this->input->post('lead_id');
		
		if (empty($lead_id)) {
			echo json_encode(['success' => false, 'message' => 'Lead ID is required']);
			return;
		}

		// Check if user can reassign this lead
		if (!apm_can_reassign_lead($lead_id)) {
			echo json_encode(['success' => false, 'message' => _l('apm_reassign_permission_denied')]);
			return;
		}

		// Get current assignment to exclude from list
		$this->db->where('id', $lead_id);
		$lead = $this->db->get(db_prefix() . 'leads')->row();
		
		$current_assigned = $lead ? $lead->assigned : null;
		
		$staff = apm_get_assignable_staff($current_assigned);
		
		echo json_encode(['success' => true, 'staff' => $staff]);
	}
}
