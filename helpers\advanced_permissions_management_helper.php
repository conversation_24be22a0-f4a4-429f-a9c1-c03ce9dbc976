<?php

if ( ! function_exists('apm_get_all_slas'))
{
	function apm_get_all_slas($order_by = 'sort_order', $direction = 'ASC')
	{
		$CI =& get_instance();

		$CI->db->order_by($order_by, $direction);

		return $CI->db->get('slas')->result_array();
	}
}

if ( ! function_exists('apm_format_sla'))
{
	function apm_format_sla($id, $classes = '')
	{
		$CI =& get_instance();

		$CI->db->where('id', $id);

		$sla = $CI->db->get('slas')->row_array();

		if (empty($sla))
		{
			return '';
		} else
		{
			return '<span class="label ' . $classes . '" style="background-color:'.$sla['background_color'].';border:1px solid '.$sla['background_color'].';color:'.$sla['text_color'].'">'.$sla['name'].'</span>';
		}
	}
}
