<?php

if ( ! function_exists('apm_get_all_slas'))
{
	function apm_get_all_slas($order_by = 'sort_order', $direction = 'ASC')
	{
		$CI =& get_instance();

		$CI->db->order_by($order_by, $direction);

		return $CI->db->get('slas')->result_array();
	}
}

if ( ! function_exists('apm_format_sla'))
{
	function apm_format_sla($id, $classes = '')
	{
		$CI =& get_instance();

		$CI->db->where('id', $id);

		$sla = $CI->db->get('slas')->row_array();

		if (empty($sla))
		{
			return '';
		} else
		{
			return '<span class="label ' . $classes . '" style="background-color:'.$sla['background_color'].';border:1px solid '.$sla['background_color'].';color:'.$sla['text_color'].'">'.$sla['name'].'</span>';
		}
	}
}

if ( ! function_exists('apm_can_reassign_lead'))
{
	function apm_can_reassign_lead($lead_id, $staff_id = null)
	{
		if (is_admin()) {
			return true;
		}

		if ($staff_id === null) {
			$staff_id = get_staff_user_id();
		}

		// Check if staff has reassign permission
		if (!staff_can('reassign', 'leads')) {
			return false;
		}

		$CI =& get_instance();
		$CI->db->where('id', $lead_id);
		$lead = $CI->db->get(db_prefix() . 'leads')->row();

		if (!$lead) {
			return false;
		}

		// Check if the lead is assigned to the current staff member
		return $lead->assigned == $staff_id;
	}
}

if ( ! function_exists('apm_reassign_lead'))
{
	function apm_reassign_lead($lead_id, $new_assigned_staff_id, $current_staff_id = null)
	{
		if ($current_staff_id === null) {
			$current_staff_id = get_staff_user_id();
		}

		// Validate permission and ownership
		if (!apm_can_reassign_lead($lead_id, $current_staff_id)) {
			return false;
		}

		$CI =& get_instance();

		// Get lead details for logging
		$CI->db->where('id', $lead_id);
		$lead = $CI->db->get(db_prefix() . 'leads')->row();

		if (!$lead) {
			return false;
		}

		// Update the lead assignment
		$CI->db->where('id', $lead_id);
		$update_result = $CI->db->update(db_prefix() . 'leads', [
			'assigned' => $new_assigned_staff_id,
			'dateassigned' => date('Y-m-d H:i:s')
		]);

		if ($update_result) {
			// Log the reassignment activity
			apm_log_lead_reassignment($lead_id, $current_staff_id, $new_assigned_staff_id, $lead);
			return true;
		}

		return false;
	}
}

if ( ! function_exists('apm_log_lead_reassignment'))
{
	function apm_log_lead_reassignment($lead_id, $from_staff_id, $to_staff_id, $lead)
	{
		$CI =& get_instance();

		// Get staff names for logging
		$from_staff = $CI->staff_model->get($from_staff_id);
		$to_staff = $CI->staff_model->get($to_staff_id);

		$from_name = $from_staff ? $from_staff->firstname . ' ' . $from_staff->lastname : 'Unknown';
		$to_name = $to_staff ? $to_staff->firstname . ' ' . $to_staff->lastname : 'Unknown';

		// Log detailed activity
		$description = sprintf(
			_l('apm_lead_reassigned_from_to'),
			$lead->name,
			$from_name,
			$to_name
		);

		// Log the main activity
		log_activity($description, $lead_id, 'lead');

		// Also log a simpler activity for the lead timeline
		$simple_description = sprintf(_l('apm_lead_reassigned_activity'), $to_name);

		// Insert into lead activity log if the table exists
		if ($CI->db->table_exists(db_prefix() . 'lead_activity_log')) {
			$CI->db->insert(db_prefix() . 'lead_activity_log', [
				'leadid' => $lead_id,
				'description' => $simple_description,
				'additional_data' => json_encode([
					'from_staff_id' => $from_staff_id,
					'to_staff_id' => $to_staff_id,
					'from_staff_name' => $from_name,
					'to_staff_name' => $to_name,
					'action' => 'lead_reassigned'
				]),
				'date' => date('Y-m-d H:i:s'),
				'staffid' => $from_staff_id
			]);
		}
	}
}

if ( ! function_exists('apm_get_assignable_staff'))
{
	function apm_get_assignable_staff($exclude_staff_id = null)
	{
		$CI =& get_instance();

		$CI->db->select('staffid, firstname, lastname');
		$CI->db->where('active', 1);

		if ($exclude_staff_id) {
			$CI->db->where('staffid !=', $exclude_staff_id);
		}

		$CI->db->order_by('firstname, lastname');
		$staff = $CI->db->get(db_prefix() . 'staff')->result();

		return $staff;
	}
}
