<?php


if ( ! function_exists('apm_add_utilities_permissions'))
{
	function apm_add_utilities_permissions($core_permissions, $data)
	{
		$core_permissions['utilities'] = [
			'name' => 'Utilities',
			'capabilities' => [
				'hide_utilities' => 'Hide utilities',
				'hide_media' => 'Hide media',
				'hide_calendar' => 'Hide calendar',
			],
		];

		return $core_permissions;
	}

	hooks()->add_filter('staff_permissions', 'apm_add_utilities_permissions', 10, 2);
}

if ( ! function_exists('apm_modify_utilities_permission_menu'))
{
	/**
	 * Filter sidebar menu items
	 * @param array $items
	 * @return array
	 */
	function apm_modify_utilities_permission_menu($items)
	{
		$utilities_children = $items['utilities']['children'] ?? [];
		if (staff_can('hide_media', 'utilities'))
		{
			$utilities_children = array_filter($utilities_children, function ($child) {
				return $child['slug'] != 'media';
			});
		}

		if (staff_can('hide_calendar', 'utilities'))
		{
			$utilities_children = array_filter($utilities_children, function ($child) {
				return $child['slug'] != 'calendar';
			});
		}

		$items['utilities']['children'] = $utilities_children;

		if ( ! is_admin() && staff_can('hide_utilities', 'utilities'))
		{
			unset($items['utilities']);
		}

		return $items;
	}

	hooks()->add_filter('sidebar_menu_items', 'apm_modify_utilities_permission_menu', 9999);
}

if ( ! function_exists('apm_override_utilities_permissions'))
{
	function apm_override_utilities_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/utilities/'))
		{
			return;
		}


		if (staff_can('hide_utilities', 'utilities'))
		{
			apm_deny_request('hide_utilities', 'utilities');
		}

		if (staff_can('hide_media', 'utilities') && str_contains($request_uri, '/admin/utilities/media'))
		{
			apm_deny_request('hide_media', 'utilities');
		}

		if (staff_can('hide_calendar', 'utilities') && str_contains($request_uri, '/admin/utilities/calendar'))
		{
			apm_deny_request('hide_calendar', 'utilities');
		}
	}

	hooks()->add_action('admin_init', 'apm_override_utilities_permissions');
}