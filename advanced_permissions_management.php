<?php

/**
 * Ensures that the module init file can't be accessed directly, only within the application.
 */

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Advanced Permissions Management
Description: Advanced Permissions Management module for Perfex - Zegaware
Version: 1.0.2
Requires at least: 1.0.*
*/

const ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME = 'advanced_permissions_management';

$CI = &get_instance();

$CI->load->helper(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/migration_log');
$CI->load->helper(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/advanced_permissions_management');
$CI->load->library(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/Zegaware_license');

register_language_files('advanced_permissions_management', ['advanced_permissions_management']);

function apm_module_activation_hook()
{
	$CI = &get_instance();
	require_once(__DIR__.'/install.php');
}

register_activation_hook(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME, 'apm_module_activation_hook');

function apm_module_register_uninstall_hook()
{
	if (get_option('apm_migrated_database'))
	{
		require_once APP_MODULES_PATH.'advanced_permissions_management/migrations/100_version_100.php';

		$migration = new Migration_Version_100();
		$migration->down();
	}
}

register_uninstall_hook(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME, 'apm_module_register_uninstall_hook');


function apm_add_license_link_to_module_list(array $action_links)
{
	$action_links[] = '<a href="'.admin_url(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/license').'">'._l('apm_zegaware_license').'</a>';

	return $action_links;
}

hooks()->add_filter("module_advanced_permissions_management_action_links", 'apm_add_license_link_to_module_list');

function zegaware_apm_check_license()
{
	$request_uri = $_SERVER['REQUEST_URI'];

	if (str_contains($request_uri, '/admin/'.ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME)
		|| str_contains($request_uri, '/admin/tickets'))
	{
		$is_activated = Zegaware_license::is_activated(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME);

		if ( ! $is_activated
			&& ! str_contains($request_uri, '/admin/'.ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/license'))
		{
			redirect(admin_url(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/license'));
			exit();
		}

		if ($is_activated)
		{
			$last_validate = get_option(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'_last_validate');

			if (empty($last_validate))
			{
				validate_zegaware_apm_license();
			} else
			{
				$last_validate = json_decode($last_validate);

				if ( ! isset($last_validate->date) || $last_validate->date !== date('Y-m-d'))
				{
					validate_zegaware_apm_license();
				}
			}
		}
	}
}

hooks()->add_action('admin_init', 'zegaware_apm_check_license');

function validate_zegaware_apm_license(): bool
{
	$validated = Zegaware_license::validate_current_license(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME);
	if ( ! $validated)
	{
		update_option(
			ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'_last_validate',
			json_encode(['date' => date('Y-m-d'), 'msg' => 'error'])
		);
		set_alert('danger', _l('require_license'));
		redirect(admin_url(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/license'));
	}

	return $validated;
}


if ( ! function_exists('apm_deny_request'))
{
	function apm_deny_request($permission, $feature)
	{
		$CI = &get_instance();
		if ($CI->input->is_ajax_request())
		{
			ajax_access_denied();
		} else
		{
			set_alert('danger', _l('access_denied'));

			$permission = sprintf('%s_%s', $permission, $feature);
			log_activity('Tried to access page where don\'t have permission'.($permission != '' ? ' ['.$permission.']' : ''));

			redirect(admin_url('access_denied'));
			exit();
		}
	}
}

$apm_license_activated = Zegaware_license::is_activated(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME);

if ($apm_license_activated)
{
	$function_path = APP_MODULES_PATH.ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME.'/inc/admin/*.php';
	$function_files = glob($function_path);

	foreach ($function_files as $file)
	{
		$version = substr(basename($file), 0, 3);

		require_once $file;
	}
}