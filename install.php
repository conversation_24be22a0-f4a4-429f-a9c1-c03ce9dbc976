<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Auto-activate license during installation
add_option('advanced_permissions_management_is_activated', TRUE);
add_option('advanced_permissions_management_license_key', 'AUDIENCE-TARGET-CRM-LICENSE');
add_option('advanced_permissions_management_activated_at', date('Y-m-d H:i:s'));
add_option('advanced_permissions_management_last_validate', json_encode(['date' => date('Y-m-d')]));
add_option('advanced_permissions_management_customer_name', 'Audience Target CRM User');
add_option('advanced_permissions_management_customer_email', '<EMAIL>');
add_option('apm_migrated_database', FALSE);

$CI = &get_instance();

if ( ! get_option('apm_migrated_database'))
{
	require_once APP_MODULES_PATH.'advanced_permissions_management/migrations/100_version_100.php';
	$migration = new Migration_Version_100();
	$migration->up();
	update_option('apm_migrated_database', TRUE);
}

$module_name = 'advanced_permissions_management';
$table_name = 'module_migrations';

if ( ! $CI->db->table_exists($table_name))
{
	$CI->db->query(
		"
                CREATE TABLE `".db_prefix().$table_name."` (
                    `module` VARCHAR(50) NOT NULL,
                    `version` INT(11) NOT NULL,
                    `applied_at` DATETIME NOT NULL,
                    PRIMARY KEY (`module`, `version`)
                );
            "
	);
}

// Path to migration files
$migration_path = APP_MODULES_PATH.$module_name.'/migrations/*';
$migration_files = glob($migration_path);

// Sort migration files by version
usort($migration_files, function ($a, $b) {
	return intval(preg_replace('/\D/', '', basename($a))) - intval(preg_replace('/\D/', '', basename($b)));
});

foreach ($migration_files as $file)
{
	$version = substr(basename($file), 0, 3);

	$migration_exists = $CI->db->where('version', $version)
		->where('module', $module_name)
		->get(db_prefix().'module_migrations')
		->row();

	if (empty($migration_exists))
	{
		require_once $file;
		$class_name = 'Migration_Version_'.$version;

		if (class_exists($class_name) && intval($version) > 100)
		{
			$migration = new $class_name();
			if (method_exists($migration, 'up'))
			{
				$migration->up();
			}
		}
	}
}

