<?php

if ( ! function_exists('apm_add_leads_permissions'))
{
	function apm_add_leads_permissions($core_permissions, $data)
	{
		$current_capabilities = $core_permissions['leads']['capabilities'];
		$new_capabilities = [
			'hide' => 'Disables all other permissions for leads',
			'view' => 'View (Global)',
			'view_own' => 'View (Own)',
			'create' => 'Create',
			'edit' => 'Edit',
			'delete' => 'Delete',
			'change_status' => 'Change Status',
			'reassign' => 'Reassign Own Leads',
		];
		$core_permissions['leads']['capabilities'] = array_merge($new_capabilities, $current_capabilities);

		return $core_permissions;
	}

	hooks()->add_filter('staff_permissions', 'apm_add_leads_permissions', 10, 2);
}

if ( ! function_exists('apm_modify_leads_permission_menu'))
{
	/**
	 * Filter sidebar menu items
	 * @param array $items
	 * @return array
	 */
	function apm_modify_leads_permission_menu($items)
	{
		if ( ! is_admin() && staff_can('hide', 'leads'))
		{
			unset($items['leads']);
		}

		if (staff_cant('view', 'leads')
			&& staff_cant('view_own', 'leads')
			&& staff_cant('create', 'leads')
			&& staff_cant('edit', 'leads')
			&& staff_cant('delete', 'leads')
			&& staff_cant('change_status', 'leads')
		)
		{
			unset($items['leads']);
		}

		return $items;
	}

	hooks()->add_filter('sidebar_menu_items', 'apm_modify_leads_permission_menu', 9999);
}

if ( ! function_exists('apm_hide_leads_permissions'))
{
	function apm_hide_leads_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/leads'))
		{
			return;
		}

		if (staff_can('hide', 'leads') && $request_uri != '/admin/access_denied')
		{
			apm_deny_request('hide', 'leads');
		}
	}

	hooks()->add_action('admin_init', 'apm_hide_leads_permissions', 1);
}

if ( ! function_exists('apm_create_leads_permissions'))
{
	function apm_create_leads_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin()
			|| ! str_contains($request_uri, '/admin/leads/lead')
			|| str_contains($request_uri, '/admin/leads/lead/')
		)
		{
			return;
		}

		if (empty($_POST))
		{
			return;
		}

		if (staff_cant('create', 'leads') && $request_uri != '/admin/access_denied')
		{
			apm_deny_request('create', 'leads');
		}
	}

	hooks()->add_action('admin_init', 'apm_create_leads_permissions');
}

if ( ! function_exists('apm_edit_leads_permissions'))
{
	function apm_edit_leads_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/leads/lead/'))
		{
			return;
		}

		$CI = &get_instance();
		$php_self = $CI->input->server('PHP_SELF');
		$lead_id = str_replace('/admin/leads/lead/', '', $php_self);
		if (empty($lead_id))
		{
			return;
		}

		if (empty($CI->input->post()))
		{
			if (
				staff_cant('view', 'leads')
				&& staff_cant('view_own', 'leads')
				&& staff_cant('edit', 'leads')
			)
			{
				apm_deny_request('view', 'leads');
			}
		} elseif (staff_cant('edit', 'leads'))
		{
			apm_deny_request('edit', 'leads');
		} elseif (staff_cant('change_status', 'leads'))
		{
			$request_status = $CI->input->post('status');
			$lead = $CI->leads_model->get($lead_id);
			if ($request_status != $lead->status)
			{
				apm_deny_request('change_status', 'leads');
			}
		}
	}

	hooks()->add_action('admin_init', 'apm_edit_leads_permissions');
}

if ( ! function_exists('apm_change_lost_status_leads_permissions'))
{
	function apm_change_lost_status_leads_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() ||
			(
				! str_contains($request_uri, '/admin/leads/unmark_as_lost/')
				&& ! str_contains($request_uri, '/admin/leads/mark_as_lost/')
			)
		)
		{
			return;
		}

		if (staff_cant('change_status', 'leads'))
		{
			apm_deny_request('change_status', 'leads');
		}
	}

	hooks()->add_action('admin_init', 'apm_change_lost_status_leads_permissions');
}

if ( ! function_exists('apm_change_junk_status_leads_permissions'))
{
	function apm_change_junk_status_leads_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() ||
			(
				! str_contains($request_uri, '/admin/leads/mark_as_junk/')
				&& ! str_contains($request_uri, '/admin/leads/unmark_as_junk/')
			)
		)
		{
			return;
		}

		if (staff_cant('change_status', 'leads'))
		{
			apm_deny_request('change_status', 'leads');
		}
	}

	hooks()->add_action('admin_init', 'apm_change_junk_status_leads_permissions');
}


if ( ! function_exists('apm_change_lead_status_leads_permissions'))
{
	function apm_change_lead_status_leads_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/leads/update_lead_status'))
		{
			return;
		}

		$CI = &get_instance();
		$lead_id = $CI->input->post('leadid');
		if (empty($lead_id))
		{
			return;
		}

		if ( ! empty($CI->input->post()) && staff_cant('change_status', 'leads'))
		{
			apm_deny_request('change_status', 'leads');
		}
	}

	hooks()->add_action('admin_init', 'apm_change_lead_status_leads_permissions');
}

if ( ! function_exists('apm_override_leads_permissions'))
{
	function apm_override_leads_permissions($have_permission, $capability, $feature, $staff_id)
	{
		if (is_admin() || $feature !== 'leads')
		{
			return $have_permission;
		}

		$CI = &get_instance();
		$permissions = $CI->staff_model->get_staff_permissions($staff_id);

		$view_leads_permission = array_filter($permissions, function ($permission) use ($feature) {
			return $permission['capability'] == 'view' && $permission['feature'] == 'leads';
		});

		if ($capability == 'view_own' && ! empty($view_leads_permission))
		{
			return TRUE;
		}

		return $have_permission;
	}

	hooks()->add_filter('staff_can', 'apm_override_leads_permissions', 10, 4);
}

if ( ! function_exists('apm_handle_lead_reassignment'))
{
	function apm_handle_lead_reassignment()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || !str_contains($request_uri, '/admin/leads/reassign_lead'))
		{
			return;
		}

		$CI = &get_instance();

		if (!$CI->input->is_ajax_request() || !$CI->input->post()) {
			return;
		}

		$lead_id = $CI->input->post('lead_id');
		$new_assigned_staff_id = $CI->input->post('new_assigned_staff_id');

		if (empty($lead_id) || empty($new_assigned_staff_id)) {
			echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
			exit();
		}

		// Check permission and ownership
		if (!apm_can_reassign_lead($lead_id)) {
			echo json_encode(['success' => false, 'message' => 'You do not have permission to reassign this lead']);
			exit();
		}

		// Perform the reassignment
		$result = apm_reassign_lead($lead_id, $new_assigned_staff_id);

		if ($result) {
			echo json_encode(['success' => true, 'message' => 'Lead reassigned successfully']);
		} else {
			echo json_encode(['success' => false, 'message' => 'Failed to reassign lead']);
		}
		exit();
	}

	hooks()->add_action('admin_init', 'apm_handle_lead_reassignment');
}

if ( ! function_exists('apm_add_lead_reassign_button'))
{
	function apm_add_lead_reassign_button($lead)
	{
		if (is_admin() || !staff_can('reassign', 'leads')) {
			return;
		}

		$current_staff_id = get_staff_user_id();

		// Only show button if current user can reassign this lead
		if (!apm_can_reassign_lead($lead->id, $current_staff_id)) {
			return;
		}

		$CI = &get_instance();

		// Get current assignee name
		$current_assignee_name = 'Unassigned';
		if ($lead->assigned) {
			$assignee = $CI->staff_model->get($lead->assigned);
			if ($assignee) {
				$current_assignee_name = $assignee->firstname . ' ' . $assignee->lastname;
			}
		}

		?>
		<div class="btn-group pull-right mright5" style="margin-top: 5px;">
			<button type="button" class="btn btn-default btn-sm"
					onclick="openLeadReassignModal(<?php echo $lead->id; ?>, '<?php echo addslashes($lead->name); ?>', '<?php echo addslashes($current_assignee_name); ?>')"
					title="<?php echo _l('apm_reassign_lead'); ?>">
				<i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>
			</button>
		</div>
		<?php
	}

	hooks()->add_action('admin_lead_profile_after_lead_name', 'apm_add_lead_reassign_button');
}

if ( ! function_exists('apm_include_lead_reassign_modal'))
{
	function apm_include_lead_reassign_modal()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		// Only include on lead view pages
		if (!str_contains($request_uri, '/admin/leads/lead/')) {
			return;
		}

		if (is_admin() || !staff_can('reassign', 'leads')) {
			return;
		}

		// Include the modal
		$CI = &get_instance();
		$CI->load->view(ADVANCED_PERMISSIONS_MANAGEMENT_MODULE_NAME . '/leads_reassign_modal');
	}

	hooks()->add_action('admin_footer', 'apm_include_lead_reassign_modal');
}