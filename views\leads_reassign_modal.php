<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Lead Reassignment Modal -->
<div class="modal fade" id="leadReassignModal" tabindex="-1" role="dialog" aria-labelledby="leadReassignModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="leadReassignModalLabel">
                    <i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>
                </h4>
            </div>
            <div class="modal-body">
                <div id="reassign-alert-container"></div>
                <form id="leadReassignForm">
                    <input type="hidden" id="reassign_lead_id" name="lead_id" value="">
                    
                    <div class="form-group">
                        <label for="current_assignee" class="control-label"><?php echo _l('apm_current_assignee'); ?>:</label>
                        <input type="text" id="current_assignee" class="form-control" readonly>
                    </div>

                    <div class="form-group">
                        <label for="new_assigned_staff_id" class="control-label"><?php echo _l('apm_reassign_to'); ?>: <span class="text-danger">*</span></label>
                        <select id="new_assigned_staff_id" name="new_assigned_staff_id" class="form-control selectpicker" data-live-search="true" required>
                            <option value=""><?php echo _l('apm_select_staff_member'); ?></option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" id="confirm_reassignment" required>
                                <?php echo _l('apm_confirm_reassignment'); ?>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmReassignBtn" disabled>
                    <i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    var leadReassignModal = $('#leadReassignModal');
    var reassignForm = $('#leadReassignForm');
    var confirmBtn = $('#confirmReassignBtn');
    var alertContainer = $('#reassign-alert-container');
    
    // Enable/disable confirm button based on checkbox
    $('#confirm_reassignment').change(function() {
        confirmBtn.prop('disabled', !this.checked);
    });
    
    // Handle reassign button click
    confirmBtn.click(function() {
        var leadId = $('#reassign_lead_id').val();
        var newStaffId = $('#new_assigned_staff_id').val();
        
        if (!leadId || !newStaffId) {
            showReassignAlert('Please select a staff member to reassign to.', 'danger');
            return;
        }
        
        confirmBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> <?php echo _l('apm_reassigning'); ?>');
        
        $.ajax({
            url: admin_url + 'advanced_permissions_management/leads_reassign/reassign_lead',
            type: 'POST',
            data: {
                lead_id: leadId,
                new_assigned_staff_id: newStaffId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showReassignAlert(response.message, 'success');
                    setTimeout(function() {
                        leadReassignModal.modal('hide');
                        location.reload(); // Refresh the page to show updated assignment
                    }, 1500);
                } else {
                    showReassignAlert(response.message, 'danger');
                    confirmBtn.prop('disabled', false).html('<i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>');
                }
            },
            error: function() {
                showReassignAlert('An error occurred while reassigning the lead.', 'danger');
                confirmBtn.prop('disabled', false).html('<i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>');
            }
        });
    });
    
    // Reset modal when closed
    leadReassignModal.on('hidden.bs.modal', function() {
        reassignForm[0].reset();
        $('#new_assigned_staff_id').selectpicker('refresh');
        confirmBtn.prop('disabled', true).html('<i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>');
        alertContainer.empty();
    });
    
    function showReassignAlert(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible">' +
                       '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                       message + '</div>';
        alertContainer.html(alertHtml);
    }
});

// Function to open reassign modal
function openLeadReassignModal(leadId, leadName, currentAssigneeName) {
    $('#reassign_lead_id').val(leadId);
    $('#current_assignee').val(currentAssigneeName);
    $('#leadReassignModalLabel').html('<i class="fa fa-exchange"></i> <?php echo _l('apm_reassign_lead'); ?>: ' + leadName);
    
    // Load assignable staff
    $.ajax({
        url: admin_url + 'advanced_permissions_management/leads_reassign/get_assignable_staff',
        type: 'POST',
        data: { lead_id: leadId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var select = $('#new_assigned_staff_id');
                select.empty().append('<option value=""><?php echo _l('apm_select_staff_member'); ?></option>');
                
                $.each(response.staff, function(index, staff) {
                    select.append('<option value="' + staff.staffid + '">' + 
                                staff.firstname + ' ' + staff.lastname + '</option>');
                });
                
                select.selectpicker('refresh');
                $('#leadReassignModal').modal('show');
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('Failed to load staff members.');
        }
    });
}
</script>
