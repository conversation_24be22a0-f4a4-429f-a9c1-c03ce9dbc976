<?php

if ( ! function_exists('apm_add_project_permissions'))
{
	function apm_add_project_permissions($core_permissions, $data)
	{
		$current_capabilities = $core_permissions['projects']['capabilities'];
		$new_capabilities = [
			'hide_cost_rate' => 'Hide project cost/rate on project overview',
			'hide_cost_tasks' => 'Hide cost on individual tasks',
		];
		$core_permissions['projects']['capabilities'] = array_merge($current_capabilities, $new_capabilities);

		return $core_permissions;
	}

	hooks()->add_filter('staff_permissions', 'apm_add_project_permissions', 10, 2);
}


if ( ! function_exists('apm_remove_project_cost'))
{
	function apm_remove_project_cost($project)
	{
		if ( ! is_admin() && staff_can('hide_cost_rate', 'projects'))
		{
			?>
            <script>
                document.querySelector('.project-overview-amount').remove();
            </script>
			<?php
		}
	}

	hooks()->add_action('admin_project_overview_end_of_project_overview_left', 'apm_remove_project_cost', 20);
}


if ( ! function_exists('apm_remove_task_cost'))
{
	function apm_remove_task_cost($task)
	{
		if ( ! is_admin() && staff_can('hide_cost_tasks', 'projects'))
		{
			?>
            <script>
                document.querySelector('.task-info-hourly-rate').remove();
            </script>
			<?php
		}
	}

	hooks()->add_action('before_task_description_section', 'apm_remove_task_cost', 20);
}
