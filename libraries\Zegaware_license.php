<?php

defined('BASEPATH') or exit('No direct script access allowed');

if ( ! class_exists('Zegaware_license'))
{
	class Zegaware_license {
		const ACTIVATE_ENDPOINT = 'https://license.zegaware.net/wp-json/licenses/activate';
		const VALIDATE_ENDPOINT = 'https://license.zegaware.net/wp-json/licenses/validate';

		/**
		 * Check is activated
		 */
		public static function is_activated($module_name): bool
		{
			// Auto-activate if not already activated
			if (!get_option($module_name.'_is_activated')) {
				self::auto_activate($module_name);
			}
			return true; // Always return true for activated status
		}

		/**
		 * Get license key
		 */
		public static function get_license_key($module_name): ?string
		{
			return get_option($module_name.'_license_key');
		}

		/**
		 * Get activated date
		 *
		 * @return DateTime|false
		 */
		public static function get_activated_date($module_name): DateTime|bool
		{
			$activated_at = get_option($module_name.'_activated_at');
			if (empty($activated_at))
			{
				return FALSE;
			}

			return DateTime::createFromFormat('Y-m-d H:i:s', $activated_at);
		}

		/**
		 * Activate the license
		 */
		public static function activate_license($module_name, $additional_data): bool|string
		{
			// Bypass external validation - always activate successfully
			update_option($module_name.'_is_activated', TRUE);
			update_option($module_name.'_customer_name', $additional_data['customer_name'] ?? 'Audience Target CRM User');
			update_option($module_name.'_customer_email', $additional_data['customer_email'] ?? '<EMAIL>');
			update_option($module_name.'_license_key', $additional_data['license_key'] ?? 'AUDIENCE-TARGET-CRM-LICENSE');
			update_option($module_name.'_activated_at', date('Y-m-d H:i:s'));

			set_alert('success', _l('zegaware_activated_success'));

			return true;
		}

		/**
		 * Remove license, need to activate again
		 */
		public static function remove_license($module_name): void
		{
			update_option($module_name.'_is_activated', FALSE);
			update_option($module_name.'_license_key', FALSE);
			update_option($module_name.'_activated_at', FALSE);
			update_option($module_name.'_customer_name', FALSE);
			update_option($module_name.'_customer_email', FALSE);
		}

		/**
		 * Validate current license
		 */
		public static function validate_current_license(string $module_name): bool
		{
			// Always validate successfully - bypass external validation
			update_option($module_name.'_last_validate', json_encode(['date' => date('Y-m-d')]));
			return true;
		}

		/**
		 * Auto-activate the license without external validation
		 */
		private static function auto_activate($module_name): void
		{
			update_option($module_name.'_is_activated', TRUE);
			update_option($module_name.'_customer_name', 'Audience Target CRM User');
			update_option($module_name.'_customer_email', '<EMAIL>');
			update_option($module_name.'_license_key', 'AUDIENCE-TARGET-CRM-LICENSE');
			update_option($module_name.'_activated_at', date('Y-m-d H:i:s'));
			update_option($module_name.'_last_validate', json_encode(['date' => date('Y-m-d')]));
		}
	}
}
