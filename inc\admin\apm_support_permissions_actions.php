<?php

if ( ! function_exists('apm_add_support_permissions'))
{
	function apm_add_support_permissions($core_permissions, $data)
	{
		$core_permissions['supports'] = [
			'name' => 'Supports',
			'capabilities' => [
				'hide' => 'Hide Support',
				'view' => 'View(Global)',
				'view_own' => 'View (Own)',
				'create' => 'Create',
				'edit' => 'Edit',
				'delete' => 'Delete',
				'view_public' => 'View public forms',
			],
		];

		return $core_permissions;
	}

	hooks()->add_filter('staff_permissions', 'apm_add_support_permissions', 10, 2);
}


if ( ! function_exists('apm_modify_supports_permission_menu'))
{
	/**
	 * Filter sidebar menu items
	 * @param array $items
	 * @return array
	 */
	function apm_modify_supports_permission_menu($items)
	{
		if ( ! is_admin() && staff_can('hide', 'supports'))
		{
			unset($items['support']);
		}

		return $items;
	}

	hooks()->add_filter('sidebar_menu_items', 'apm_modify_supports_permission_menu', 9999);
}


if ( ! function_exists('apm_hide_supports_permissions'))
{
	function apm_hide_supports_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/tickets'))
		{
			return;
		}

		if (staff_can('hide', 'supports') && $request_uri != '/admin/access_denied')
		{
			apm_deny_request('hide', 'supports');
		}
	}

	hooks()->add_action('admin_init', 'apm_hide_supports_permissions', 9999);
}

if ( ! function_exists('apm_create_supports_permissions'))
{
	function apm_create_supports_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/tickets/add'))
		{
			return;
		}

		if (staff_cant('create', 'supports') && $request_uri != '/admin/access_denied')
		{
			apm_deny_request('create', 'supports');
		}
	}

	hooks()->add_action('admin_init', 'apm_create_supports_permissions', 100);
}

if ( ! function_exists('apm_view_edit_supports_permissions'))
{
	function apm_view_edit_supports_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin()
			|| (
				! str_contains($request_uri, 'admin/tickets')
				&& ! str_contains($request_uri, '/admin/tickets/ticket/')
			))
		{
			return;
		}

		$CI = &get_instance();
		$php_self = $CI->input->server('PHP_SELF');
		$ticket_id = str_replace('/admin/tickets/ticket/', '', $php_self);
		if (
			str_contains($request_uri, '/admin/tickets/ticket/')
			&& ! empty($ticket_id)
			&& staff_cant('view', 'supports')
			&& staff_cant('edit', 'supports')
			&& $request_uri != '/admin/access_denied'
		)
		{
			apm_deny_request('view', 'supports');
		}

		if (
			str_ends_with($request_uri, '/admin/tickets')
			&& staff_cant('view', 'supports')
			&& staff_cant('create', 'supports')
			&& $request_uri != '/admin/access_denied'
		)
		{
			apm_deny_request('view', 'supports');
		}

		$ticket_id = $CI->input->post('ticketid');
		if ($ticket_id && staff_cant('edit', 'supports'))
		{
			apm_deny_request('edit', 'supports');
		}
	}

	hooks()->add_action('admin_init', 'apm_view_edit_supports_permissions', 100);
}

if ( ! function_exists('apm_edit_supports_permissions'))
{
	function apm_edit_supports_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin()
			|| (
				! str_contains($request_uri, '/admin/tickets/ticket/')
				&& ! str_contains($request_uri, '/admin/tickets/update_single_ticket_settings')
			))
		{
			return;
		}

		$CI = &get_instance();
		$php_self = $CI->input->server('PHP_SELF');
		$ticket_id = str_replace('/admin/tickets/ticket/', '', $php_self);

		if (
			! empty($ticket_id)
			&& staff_cant('view', 'supports')
			&& staff_cant('edit', 'supports')
			&& $request_uri != '/admin/access_denied'
		)
		{
			apm_deny_request('view', 'supports');
		}

		$ticket_id = $CI->input->post('ticketid');
		if ($ticket_id && staff_cant('edit', 'supports'))
		{
			apm_deny_request('edit', 'supports');
		}
	}

	hooks()->add_action('admin_init', 'apm_edit_supports_permissions', 100);
}

if ( ! function_exists('apm_delete_supports_permissions'))
{
	function apm_delete_supports_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/admin/tickets/delete/'))
		{
			return;
		}

		$CI = &get_instance();
		$php_self = $CI->input->server('PHP_SELF');
		$ticket_id = str_replace('/admin/tickets/delete/', '', $php_self);

		if (
			! empty($ticket_id)
			&& staff_cant('delete', 'supports')
			&& $request_uri != '/admin/access_denied'
		)
		{
			apm_deny_request('delete', 'supports');
		}
	}

	hooks()->add_action('admin_init', 'apm_delete_supports_permissions', 100);
}

if ( ! function_exists('apm_view_public_form_supports_permissions'))
{
	function apm_view_public_form_supports_permissions()
	{
		$request_uri = $_SERVER['REQUEST_URI'];

		if (is_admin() || ! str_contains($request_uri, '/forms/tickets/'))
		{
			return;
		}

		$CI = &get_instance();
		$php_self = $CI->input->server('PHP_SELF');
		$ticket_hash = str_replace('/forms/tickets/', '', $php_self);

		if (
			! empty($ticket_hash)
			&& staff_cant('view_public', 'supports')
			&& $request_uri != '/admin/access_denied'
		)
		{
			apm_deny_request('view_public', 'supports');
		}
	}

	hooks()->add_action('app_init', 'apm_view_public_form_supports_permissions', 100);
}